{"results": {"mvr": {"alias": "mvr", "mvr_perception_score,none": 52.3, "mvr_perception_score_stderr,none": "N/A"}}, "group_subtasks": {"mvr": []}, "configs": {"mvr": {"task": "mvr", "dataset_path": "/root/autodl-tmp/mvr_dataset", "dataset_kwargs": {"token": true, "cache_dir": "/root/autodl-tmp/CVBench", "video": true, "load_from_disk": true}, "test_split": "test", "full_docs": false, "process_results_use_image": false, "doc_to_visual": "<function mvr_doc_to_visual at 0x7f17bb4dd2d0>", "doc_to_text": "<function mvr_doc_to_text at 0x7f17bb4ddb40>", "doc_to_target": "answer", "process_results": "<function mvr_process_results at 0x7f17bb4de680>", "description": "", "target_delimiter": " ", "fewshot_delimiter": "\n\n", "num_fewshot": 0, "metric_list": [{"metric": "mvr_perception_score", "aggregation": "<function mvr_aggregate_results at 0x7f17bb4df0a0>", "higher_is_better": true}], "output_type": "generate_until", "generation_kwargs": {"max_new_tokens": 16, "num_beams": 1, "do_sample": false, "until": ["\n\n"]}, "repeats": 1, "should_decontaminate": false, "metadata": [{"version": 0.0}], "lmms_eval_specific_kwargs": {"default": {"pre_prompt": "", "post_prompt1": "Answer with the option's letter (A, B, C, or D) from the given choices directly.", "post_prompt2": "Answer with the option's word (YES or NO) from the given choices directly."}, "pre_prompt": "", "post_prompt1": "Answer with the option's letter (A, B, C, or D) from the given choices directly.", "post_prompt2": "Answer with the option's word (YES or NO) from the given choices directly."}}}, "versions": {"mvr": "Yaml"}, "n-shot": {"mvr": 0}, "higher_is_better": {"mvr": {"mvr_perception_score": true}}, "n-samples": {"mvr": {"original": 1000, "effective": 1000}}, "config": {"model": "internvideo2_5", "model_args": "", "batch_size": "1", "batch_sizes": [], "device": null, "use_cache": null, "limit": null, "bootstrap_iters": 100000, "gen_kwargs": "", "random_seed": 0, "numpy_seed": 1234, "torch_seed": 1234, "fewshot_seed": 1234}, "git_hash": null, "date": "20250730_180721", "task_hashes": {}, "model_source": "internvideo2_5", "model_name": "", "model_name_sanitized": "", "system_instruction": null, "system_instruction_sha": null, "fewshot_as_multiturn": false, "chat_template": null, "chat_template_sha": null, "start_time": 36623905.35105419, "end_time": 36628232.76063932, "total_evaluation_time_seconds": "4327.409585125744"}