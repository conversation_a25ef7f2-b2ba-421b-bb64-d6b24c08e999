#!/usr/bin/env python3
"""
Script to extract specific fields (id, task_type, pred_answer, answer) from JSON/JSONL files.
Supports both single JSON files and JSONL (JSON Lines) format.
"""

import json
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional


def extract_fields_from_dict(data: Dict[str, Any], target_fields: List[str]) -> Optional[Dict[str, Any]]:
    """
    Extract target fields from a dictionary.
    
    Args:
        data: Dictionary to extract fields from
        target_fields: List of field names to extract
        
    Returns:
        Dictionary with extracted fields, or None if no target fields found
    """
    extracted = {}
    for field in target_fields:
        if field in data:
            extracted[field] = data[field]
    
    return extracted if extracted else None


def search_nested_dict(data: Any, target_fields: List[str]) -> List[Dict[str, Any]]:
    """
    Recursively search for target fields in nested dictionaries and lists.
    
    Args:
        data: Data structure to search (dict, list, or other)
        target_fields: List of field names to extract
        
    Returns:
        List of dictionaries containing extracted fields
    """
    results = []
    
    if isinstance(data, dict):
        # Try to extract fields from current level
        extracted = extract_fields_from_dict(data, target_fields)
        if extracted:
            results.append(extracted)
        
        # Recursively search nested structures
        for value in data.values():
            results.extend(search_nested_dict(value, target_fields))
    
    elif isinstance(data, list):
        for item in data:
            results.extend(search_nested_dict(item, target_fields))
    
    return results


def process_json_file(file_path: str, target_fields: List[str]) -> List[Dict[str, Any]]:
    """
    Process a JSON file and extract target fields.
    
    Args:
        file_path: Path to the JSON file
        target_fields: List of field names to extract
        
    Returns:
        List of dictionaries containing extracted fields
    """
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            results = search_nested_dict(data, target_fields)
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON file: {e}")
        return []
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error reading file: {e}")
        return []
    
    return results


def process_jsonl_file(file_path: str, target_fields: List[str]) -> List[Dict[str, Any]]:
    """
    Process a JSONL file and extract target fields.
    
    Args:
        file_path: Path to the JSONL file
        target_fields: List of field names to extract
        
    Returns:
        List of dictionaries containing extracted fields
    """
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    data = json.loads(line)
                    extracted = extract_fields_from_dict(data, target_fields)
                    if extracted:
                        results.append(extracted)
                except json.JSONDecodeError as e:
                    print(f"Error parsing line {line_num}: {e}")
                    continue
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error reading file: {e}")
        return []
    
    return results


def analyze_file_structure(file_path: str):
    """
    Analyze and display the structure of a JSON/JSONL file.

    Args:
        file_path: Path to the file to analyze
    """
    print(f"\nAnalyzing file structure: {file_path}")
    print("=" * 50)

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_path.endswith('.jsonl'):
                print("File type: JSONL (JSON Lines)")
                print("\nFirst few lines structure:")
                for i, line in enumerate(f):
                    if i >= 5:  # Show first 5 lines
                        break
                    if line.strip():
                        try:
                            data = json.loads(line.strip())
                            if isinstance(data, dict):
                                print(f"Line {i+1} keys: {list(data.keys())}")
                            else:
                                print(f"Line {i+1}: {type(data).__name__}")
                        except json.JSONDecodeError:
                            print(f"Line {i+1}: Invalid JSON")
            else:
                print("File type: JSON")
                data = json.load(f)

                def show_structure(obj, prefix="", max_depth=3, current_depth=0):
                    if current_depth >= max_depth:
                        return

                    if isinstance(obj, dict):
                        print(f"{prefix}Dictionary with {len(obj)} keys:")
                        for key, value in list(obj.items())[:10]:  # Show first 10 keys
                            type_info = type(value).__name__
                            if isinstance(value, (dict, list)):
                                size_info = f" (size: {len(value)})" if hasattr(value, '__len__') else ""
                                print(f"{prefix}  '{key}': {type_info}{size_info}")
                                if current_depth < max_depth - 1:
                                    show_structure(value, prefix + "    ", max_depth, current_depth + 1)
                            else:
                                print(f"{prefix}  '{key}': {type_info}")
                        if len(obj) > 10:
                            print(f"{prefix}  ... and {len(obj) - 10} more keys")

                    elif isinstance(obj, list):
                        print(f"{prefix}List with {len(obj)} items")
                        if obj and current_depth < max_depth - 1:
                            print(f"{prefix}  First item type: {type(obj[0]).__name__}")
                            if isinstance(obj[0], dict):
                                print(f"{prefix}  First item keys: {list(obj[0].keys())}")

                show_structure(data)

    except Exception as e:
        print(f"Error analyzing file: {e}")


def save_results(results: List[Dict[str, Any]], output_file: str, format_type: str = 'json'):
    """
    Save extracted results to a file.
    
    Args:
        results: List of extracted data
        output_file: Output file path
        format_type: Output format ('json' or 'jsonl')
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            if format_type == 'jsonl':
                for item in results:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
            else:
                json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"Results saved to: {output_file}")
        print(f"Total records extracted: {len(results)}")
    except Exception as e:
        print(f"Error saving results: {e}")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Extract specific fields from JSON/JSONL files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Extract default fields from a specific file
  python extract_fields.py input.json

  # Extract custom fields
  python extract_fields.py input.json --fields id name score

  # Process JSONL file with custom output
  python extract_fields.py data.jsonl --fields user_id response --output results.json

  # Show file structure without extracting
  python extract_fields.py input.json --analyze-only
        """
    )

    parser.add_argument(
        'input_file',
        nargs='?',
        default="20250730_180721_results.json",
        help='Input JSON or JSONL file path (default: 20250730_180721_results.json)'
    )

    parser.add_argument(
        '--fields', '-f',
        nargs='+',
        default=['id', 'task_type', 'pred_answer', 'answer'],
        help='Fields to extract (default: id task_type pred_answer answer)'
    )

    parser.add_argument(
        '--output', '-o',
        help='Output file path (default: extracted_<input_filename>.json)'
    )

    parser.add_argument(
        '--format',
        choices=['json', 'jsonl'],
        default='json',
        help='Output format (default: json)'
    )

    parser.add_argument(
        '--analyze-only',
        action='store_true',
        help='Only analyze file structure without extracting data'
    )

    return parser.parse_args()


def main():
    """Main function to process the file and extract fields."""

    # Parse command line arguments
    args = parse_arguments()

    # Get parameters from arguments
    input_file = args.input_file
    target_fields = args.fields
    
    # Check if file exists
    if not Path(input_file).exists():
        print(f"File not found: {input_file}")
        print("Please make sure the file exists in the current directory.")
        return

    print(f"Processing file: {input_file}")
    print(f"Looking for fields: {', '.join(target_fields)}")
    print("-" * 50)

    # If analyze-only mode, just show structure
    if args.analyze_only:
        analyze_file_structure(input_file)
        return

    # Determine file type and process accordingly
    if input_file.endswith('.jsonl'):
        results = process_jsonl_file(input_file, target_fields)
    else:
        results = process_json_file(input_file, target_fields)
    
    if not results:
        print("No records found with the target fields.")
        print("\nLet me analyze the file structure...")
        
        # Analyze file structure
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                if input_file.endswith('.jsonl'):
                    # For JSONL, analyze first few lines
                    print("\nFirst few lines structure:")
                    for i, line in enumerate(f):
                        if i >= 3:  # Show first 3 lines
                            break
                        if line.strip():
                            data = json.loads(line.strip())
                            print(f"Line {i+1} keys: {list(data.keys())}")
                else:
                    # For JSON, show top-level structure
                    data = json.load(f)
                    print(f"\nTop-level keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dictionary'}")
                    
                    # If it's a dict, show some nested structure
                    if isinstance(data, dict):
                        for key, value in data.items():
                            if isinstance(value, dict):
                                print(f"'{key}' contains: {list(value.keys())}")
                            elif isinstance(value, list) and value and isinstance(value[0], dict):
                                print(f"'{key}' is a list of dicts with keys: {list(value[0].keys())}")
        except Exception as e:
            print(f"Error analyzing file structure: {e}")
        
        return
    
    # Generate output filename
    if args.output:
        output_file = args.output
    else:
        input_path = Path(input_file)
        output_file = f"extracted_{input_path.stem}.json"

    # Save results
    save_results(results, output_file, args.format)
    
    # Show sample of results
    if results:
        print(f"\nSample of extracted data:")
        for i, item in enumerate(results[:3]):  # Show first 3 items
            print(f"Record {i+1}: {item}")
        
        if len(results) > 3:
            print(f"... and {len(results) - 3} more records")


if __name__ == "__main__":
    main()
