#!/usr/bin/env python3
"""
Script to extract specific fields (id, task_type, pred_answer, answer) from JSON/JSONL files.
Supports both single JSON files and JSONL (JSON Lines) format.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional


def extract_fields_from_dict(data: Dict[str, Any], target_fields: List[str]) -> Optional[Dict[str, Any]]:
    """
    Extract target fields from a dictionary.
    
    Args:
        data: Dictionary to extract fields from
        target_fields: List of field names to extract
        
    Returns:
        Dictionary with extracted fields, or None if no target fields found
    """
    extracted = {}
    for field in target_fields:
        if field in data:
            extracted[field] = data[field]
    
    return extracted if extracted else None


def search_nested_dict(data: Any, target_fields: List[str]) -> List[Dict[str, Any]]:
    """
    Recursively search for target fields in nested dictionaries and lists.
    
    Args:
        data: Data structure to search (dict, list, or other)
        target_fields: List of field names to extract
        
    Returns:
        List of dictionaries containing extracted fields
    """
    results = []
    
    if isinstance(data, dict):
        # Try to extract fields from current level
        extracted = extract_fields_from_dict(data, target_fields)
        if extracted:
            results.append(extracted)
        
        # Recursively search nested structures
        for value in data.values():
            results.extend(search_nested_dict(value, target_fields))
    
    elif isinstance(data, list):
        for item in data:
            results.extend(search_nested_dict(item, target_fields))
    
    return results


def process_json_file(file_path: str, target_fields: List[str]) -> List[Dict[str, Any]]:
    """
    Process a JSON file and extract target fields.
    
    Args:
        file_path: Path to the JSON file
        target_fields: List of field names to extract
        
    Returns:
        List of dictionaries containing extracted fields
    """
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            results = search_nested_dict(data, target_fields)
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON file: {e}")
        return []
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error reading file: {e}")
        return []
    
    return results


def process_jsonl_file(file_path: str, target_fields: List[str]) -> List[Dict[str, Any]]:
    """
    Process a JSONL file and extract target fields.
    
    Args:
        file_path: Path to the JSONL file
        target_fields: List of field names to extract
        
    Returns:
        List of dictionaries containing extracted fields
    """
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    data = json.loads(line)
                    extracted = extract_fields_from_dict(data, target_fields)
                    if extracted:
                        results.append(extracted)
                except json.JSONDecodeError as e:
                    print(f"Error parsing line {line_num}: {e}")
                    continue
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error reading file: {e}")
        return []
    
    return results


def save_results(results: List[Dict[str, Any]], output_file: str, format_type: str = 'json'):
    """
    Save extracted results to a file.
    
    Args:
        results: List of extracted data
        output_file: Output file path
        format_type: Output format ('json' or 'jsonl')
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            if format_type == 'jsonl':
                for item in results:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
            else:
                json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"Results saved to: {output_file}")
        print(f"Total records extracted: {len(results)}")
    except Exception as e:
        print(f"Error saving results: {e}")


def main():
    """Main function to process the file and extract fields."""
    
    # Target fields to extract
    target_fields = ['id', 'task_type', 'pred_answer', 'answer']
    
    # Input file (you can modify this or pass as command line argument)
    input_file = "20250730_180721_results.json"
    
    # Check if file exists
    if not Path(input_file).exists():
        print(f"File not found: {input_file}")
        print("Please make sure the file exists in the current directory.")
        return
    
    print(f"Processing file: {input_file}")
    print(f"Looking for fields: {', '.join(target_fields)}")
    print("-" * 50)
    
    # Determine file type and process accordingly
    if input_file.endswith('.jsonl'):
        results = process_jsonl_file(input_file, target_fields)
    else:
        results = process_json_file(input_file, target_fields)
    
    if not results:
        print("No records found with the target fields.")
        print("\nLet me analyze the file structure...")
        
        # Analyze file structure
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                if input_file.endswith('.jsonl'):
                    # For JSONL, analyze first few lines
                    print("\nFirst few lines structure:")
                    for i, line in enumerate(f):
                        if i >= 3:  # Show first 3 lines
                            break
                        if line.strip():
                            data = json.loads(line.strip())
                            print(f"Line {i+1} keys: {list(data.keys())}")
                else:
                    # For JSON, show top-level structure
                    data = json.load(f)
                    print(f"\nTop-level keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dictionary'}")
                    
                    # If it's a dict, show some nested structure
                    if isinstance(data, dict):
                        for key, value in data.items():
                            if isinstance(value, dict):
                                print(f"'{key}' contains: {list(value.keys())}")
                            elif isinstance(value, list) and value and isinstance(value[0], dict):
                                print(f"'{key}' is a list of dicts with keys: {list(value[0].keys())}")
        except Exception as e:
            print(f"Error analyzing file structure: {e}")
        
        return
    
    # Generate output filename
    input_path = Path(input_file)
    output_file = f"extracted_{input_path.stem}.json"
    
    # Save results
    save_results(results, output_file)
    
    # Show sample of results
    if results:
        print(f"\nSample of extracted data:")
        for i, item in enumerate(results[:3]):  # Show first 3 items
            print(f"Record {i+1}: {item}")
        
        if len(results) > 3:
            print(f"... and {len(results) - 3} more records")


if __name__ == "__main__":
    main()
