#!/usr/bin/env python3
"""
Fix for sequence length and Triton CUDA errors in Qwen2.5-VL
This script provides multiple solutions for the token sequence length issue
"""

import os
import sys
import argparse
import subprocess

def set_environment_variables():
    """Set environment variables to help with CUDA issues"""
    env_vars = {
        'CUDA_LAUNCH_BLOCKING': '1',
        'TORCH_USE_CUDA_DSA': '1',
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:128',
        'TOKENIZERS_PARALLELISM': 'false',  # Avoid tokenizer warnings
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"Set {key}={value}")

def get_conservative_model_args():
    """Get very conservative model arguments to avoid sequence length issues"""
    return ",".join([
        "pretrained=Qwen/Qwen2.5-VL-7B-Instruct",
        "use_flash_attention_2=False",  # Disable flash attention
        "max_pixels=50176",  # 224x224 resolution
        "fps=1.0",  # 1 fps
        "max_image_size=224",  # Smaller image size
        "max_num_frames=4",  # Reduce frames significantly
        "max_new_tokens=64",  # Very short output
        "truncation=True",  # Enable truncation
        "use_custom_video_loader=True",
        "attn_implementation=eager",  # Use eager attention
    ])

def get_medium_model_args():
    """Get medium conservative model arguments"""
    return ",".join([
        "pretrained=Qwen/Qwen2.5-VL-7B-Instruct",
        "use_flash_attention_2=False",
        "max_pixels=147456",  # 384x384 resolution
        "fps=1.0",
        "max_image_size=384",
        "max_num_frames=6",  # Moderate frame reduction
        "max_new_tokens=128",  # Moderate output length
        "truncation=True",
        "use_custom_video_loader=True",
        "attn_implementation=eager",
    ])

def run_with_fallback(task, output_path="./results", verbosity="INFO"):
    """Run evaluation with automatic fallback strategies"""
    
    print("🚀 Starting Qwen2.5-VL evaluation with fallback strategies")
    print("=" * 60)
    
    # Set environment variables
    set_environment_variables()
    
    strategies = [
        {
            "name": "Conservative (224x224, 4 frames, 64 tokens)",
            "model_args": get_conservative_model_args(),
            "batch_size": 1
        },
        {
            "name": "Medium (384x384, 6 frames, 128 tokens)",
            "model_args": get_medium_model_args(),
            "batch_size": 1
        }
    ]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"\n📋 Strategy {i}: {strategy['name']}")
        print("-" * 50)
        
        cmd = [
            "python", "-m", "lmms_eval",
            "--model", "qwen2_5_vl",
            "--model_args", strategy["model_args"],
            "--tasks", task,
            "--batch_size", str(strategy["batch_size"]),
            "--output_path", output_path,
            "--verbosity", verbosity,
            "--log_samples",
        ]
        
        print("Command:")
        print(" ".join(cmd))
        print()
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=False)
            print(f"✅ Strategy {i} succeeded!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Strategy {i} failed with return code {e.returncode}")
            if i < len(strategies):
                print(f"🔄 Trying next strategy...")
            continue
        except Exception as e:
            print(f"❌ Strategy {i} failed with error: {e}")
            if i < len(strategies):
                print(f"🔄 Trying next strategy...")
            continue
    
    print("\n💥 All strategies failed!")
    return False

def analyze_error_log(log_file=None):
    """Analyze error logs and provide specific suggestions"""
    print("\n🔍 Error Analysis and Suggestions")
    print("=" * 50)
    
    suggestions = [
        "1. Sequence Length Issues:",
        "   - Reduce max_num_frames (try 2-4 frames)",
        "   - Use smaller resolution (224x224 instead of 384x384)",
        "   - Limit max_new_tokens to 32-64",
        "   - Enable truncation in model args",
        "",
        "2. Triton CUDA Errors:",
        "   - Set use_flash_attention_2=False",
        "   - Use attn_implementation=eager",
        "   - Set environment variables (already done)",
        "   - Consider using CPU fallback",
        "",
        "3. Memory Issues:",
        "   - Use batch_size=1",
        "   - Reduce video resolution",
        "   - Clear GPU cache: torch.cuda.empty_cache()",
        "",
        "4. Model Loading Issues:",
        "   - Use torch_dtype=float16",
        "   - Disable flash attention completely",
        "   - Use device_map='auto' or specific GPU",
        "",
        "5. Video Processing Issues:",
        "   - Reduce fps to 0.5 or 1.0",
        "   - Use fewer frames per video",
        "   - Check video file formats",
    ]
    
    for suggestion in suggestions:
        print(suggestion)

def create_minimal_test():
    """Create a minimal test script"""
    test_script = '''
import torch
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor

# Test minimal model loading
print("Testing minimal Qwen2.5-VL loading...")

try:
    # Very conservative settings
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        "Qwen/Qwen2.5-VL-7B-Instruct",
        torch_dtype=torch.float16,
        device_map="auto",
        attn_implementation="eager",  # Avoid flash attention
    )
    
    processor = AutoProcessor.from_pretrained(
        "Qwen/Qwen2.5-VL-7B-Instruct",
        max_pixels=50176,  # 224x224
        min_pixels=128*28*28
    )
    
    print("✅ Model loaded successfully!")
    print(f"Model config max_position_embeddings: {model.config.max_position_embeddings}")
    
    # Test a simple text input
    messages = [{"role": "user", "content": [{"type": "text", "text": "Hello"}]}]
    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    inputs = processor(text=[text], return_tensors="pt")
    
    print(f"Input token length: {inputs.input_ids.shape[1]}")
    
    if inputs.input_ids.shape[1] < 1000:  # Only generate if input is short
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=32,
                do_sample=False,
                use_cache=False
            )
        print("✅ Generation test passed!")
    else:
        print("⚠️ Input too long, skipping generation test")
        
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
'''
    
    with open("test_minimal_qwen.py", "w") as f:
        f.write(test_script)
    
    print("📝 Created test_minimal_qwen.py")
    print("Run: python test_minimal_qwen.py")

def main():
    parser = argparse.ArgumentParser(description="Fix sequence length and Triton errors")
    parser.add_argument("--task", help="Evaluation task to run")
    parser.add_argument("--output_path", default="./results", help="Output path")
    parser.add_argument("--verbosity", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       default="INFO", help="Logging verbosity")
    parser.add_argument("--analyze", action="store_true", help="Only show error analysis")
    parser.add_argument("--test", action="store_true", help="Create minimal test script")
    
    args = parser.parse_args()
    
    if args.analyze:
        analyze_error_log()
        return
    
    if args.test:
        create_minimal_test()
        return
    
    if not args.task:
        print("❌ Please specify a task with --task")
        print("\nAvailable options:")
        print("  --task your_task_name    # Run evaluation with fallback")
        print("  --analyze               # Show error analysis")
        print("  --test                  # Create minimal test script")
        return
    
    success = run_with_fallback(args.task, args.output_path, args.verbosity)
    
    if not success:
        print("\n📋 Next Steps:")
        print("1. Run: python fix_sequence_length_error.py --analyze")
        print("2. Run: python fix_sequence_length_error.py --test")
        print("3. Check GPU memory: nvidia-smi")
        print("4. Try with even smaller settings manually")

if __name__ == "__main__":
    main()
