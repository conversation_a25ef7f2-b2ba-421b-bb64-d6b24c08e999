#!/bin/bash
# Quick fix commands for Qwen2.5-VL sequence length and Triton errors

echo "🚨 Quick Fix Commands for Qwen2.5-VL Errors"
echo "============================================="

# Set environment variables
export CUDA_LAUNCH_BLOCKING=1
export TORCH_USE_CUDA_DSA=1
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export TOKENIZERS_PARALLELISM=false

echo "✅ Environment variables set"

echo ""
echo "🔧 Option 1: Ultra Conservative (Most Likely to Work)"
echo "======================================================"
echo "python -m lmms_eval \\"
echo "    --model qwen2_5_vl \\"
echo "    --model_args pretrained=Qwen/Qwen2.5-VL-7B-Instruct,use_flash_attention_2=False,max_pixels=50176,fps=1.0,max_image_size=224,max_num_frames=2,max_new_tokens=32,use_custom_video_loader=True,attn_implementation=eager \\"
echo "    --tasks YOUR_TASK \\"
echo "    --batch_size 1 \\"
echo "    --verbosity DEBUG"

echo ""
echo "🔧 Option 2: Conservative"
echo "========================="
echo "python -m lmms_eval \\"
echo "    --model qwen2_5_vl \\"
echo "    --model_args pretrained=Qwen/Qwen2.5-VL-7B-Instruct,use_flash_attention_2=False,max_pixels=50176,fps=1.0,max_image_size=224,max_num_frames=4,max_new_tokens=64,use_custom_video_loader=True \\"
echo "    --tasks YOUR_TASK \\"
echo "    --batch_size 1"

echo ""
echo "🔧 Option 3: Medium (if above work)"
echo "==================================="
echo "python -m lmms_eval \\"
echo "    --model qwen2_5_vl \\"
echo "    --model_args pretrained=Qwen/Qwen2.5-VL-7B-Instruct,use_flash_attention_2=False,max_pixels=147456,fps=1.0,max_image_size=384,max_num_frames=6,max_new_tokens=128,use_custom_video_loader=True \\"
echo "    --tasks YOUR_TASK \\"
echo "    --batch_size 1"

echo ""
echo "🛠️ Automated Fix:"
echo "=================="
echo "python fix_sequence_length_error.py --task YOUR_TASK"

echo ""
echo "🧪 Test Model Loading:"
echo "======================"
echo "python fix_sequence_length_error.py --test"
echo "python test_minimal_qwen.py"

echo ""
echo "📊 Error Analysis:"
echo "=================="
echo "python fix_sequence_length_error.py --analyze"

echo ""
echo "💡 Key Points:"
echo "=============="
echo "- Replace YOUR_TASK with your actual task name"
echo "- Start with Option 1 (ultra conservative)"
echo "- If it works, gradually try higher options"
echo "- Use --verbosity DEBUG to see detailed errors"
echo "- Monitor GPU memory with: nvidia-smi"
