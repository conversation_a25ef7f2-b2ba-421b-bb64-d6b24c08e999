# Qwen2.5-VL 配置总结：fps=1 和 384x384 分辨率

## 📋 配置概览

本文档总结了为 Qwen2.5-VL 模型配置 **fps=1** 和 **384x384 分辨率** 的所有修改。

## 🔧 主要修改

### 1. 默认参数设置 (`qwen2_5_vl.py`)

```python
# 修改前
max_pixels: int = 1605632  # 原始高分辨率
use_custom_video_loader: Optional[bool] = False
fps: Optional[float] = None
max_image_size: Optional[int] = None

# 修改后
max_pixels: int = 384 * 384  # 384x384 = 147,456 像素
use_custom_video_loader: Optional[bool] = True  # 启用自定义加载器
fps: Optional[float] = 1.0  # 默认 1 fps
max_image_size: Optional[int] = 384  # 384x384 分辨率
```

### 2. 视频处理逻辑优化

#### 单个视频处理
```python
# 自定义视频加载器路径
if self.use_custom_video_loader:
    visual = read_video_pyav_base64(
        visual, 
        num_frm=self.max_num_frames, 
        fps=self.fps,  # 使用 fps=1
        img_format="JPEG", 
        max_image_size=self.max_image_size  # 使用 384
    )

# 默认加载器路径
else:
    max_pixels = 384 * 384  # 强制 384x384
    message.append({
        "role": "user", 
        "content": [
            {"type": "video", "video": visual, "max_pixels": max_pixels, "fps": 1}
        ]
    })
```

#### 多个视频处理
```python
# 每个视频都使用相同配置
max_pixels = 384 * 384
video_content.append({
    "type": "video", 
    "video": f"{v}", 
    "max_pixels": max_pixels, 
    "fps": 1
})
```

### 3. 参数验证逻辑

```python
# 自动设置默认值
if self.use_custom_video_loader:
    if self.fps is None:
        self.fps = 1.0  # 默认 fps
    if self.max_image_size is None:
        self.max_image_size = 384  # 默认分辨率
```

## 🚀 使用方法

### 方法 1：lmms-eval 命令行

```bash
python -m lmms_eval \
    --model qwen2_5_vl \
    --model_args pretrained=Qwen/Qwen2.5-VL-7B-Instruct,max_pixels=147456,fps=1.0,max_image_size=384,use_custom_video_loader=True \
    --tasks mvr \
    --batch_size 1 \
    --log_samples \
    --output_path ./results/
```

### 方法 2：Python 代码

```python
from qwen2_5_vl import Qwen2_5_VL

# 现在默认就是我们想要的配置
model = Qwen2_5_VL(
    pretrained="Qwen/Qwen2.5-VL-7B-Instruct"
    # 其他参数会自动使用优化后的默认值
)

# 或者显式指定
model = Qwen2_5_VL(
    pretrained="Qwen/Qwen2.5-VL-7B-Instruct",
    max_pixels=384 * 384,
    fps=1.0,
    max_image_size=384,
    use_custom_video_loader=True
)
```

## 💡 配置优势

### 1. **内存效率**
- **384x384** vs 原始 **1605632** 像素
- 内存使用减少约 **91%**
- 支持更大的 batch size

### 2. **计算效率**
- **fps=1** 大幅减少需要处理的帧数
- 推理速度显著提升
- 适合大规模评估

### 3. **质量平衡**
- 384x384 提供更好的图像质量
- 保持模型的核心能力
- 在效率和质量间取得良好平衡

## 🔄 两种视频加载器对比

| 特性 | 默认加载器 | 自定义加载器 ✅ |
|------|------------|-----------------|
| 控制精度 | 中等 | 高 |
| fps 控制 | 模型内部 | 精确控制 |
| 分辨率控制 | 模型内部 | 精确控制 |
| 预处理开销 | 低 | 中等 |
| 推荐使用 | 快速测试 | 正式实验 |

## 📁 相关文件

1. **`qwen2_5_vl.py`** - 主要模型文件（已修改）
2. **`test_qwen2_5_vl_config.py`** - 配置测试脚本
3. **`run_qwen2_5_vl_experiment.py`** - 实验运行示例
4. **`extract_fields.py`** - 结果提取工具

## 🧪 验证配置

运行测试脚本验证配置：

```bash
python test_qwen2_5_vl_config.py
```

预期输出：
```
✓ Model initialized successfully
✓ Max pixels: 147456 (should be 147456)
✓ FPS: 1.0 (should be 1.0)
✓ Max image size: 384 (should be 384)
✓ Use custom video loader: True
```

## 🎯 实验建议

1. **首次运行**：使用小数据集测试配置
2. **监控资源**：观察 GPU 内存和计算时间
3. **对比基线**：与原始配置对比性能
4. **调整参数**：根据具体任务需求微调

## ⚠️ 注意事项

1. **依赖检查**：确保安装 `qwen-vl-utils`
2. **GPU 内存**：虽然减少了，仍需足够的 VRAM
3. **数据格式**：确保视频文件格式兼容
4. **路径设置**：检查视频文件路径正确性

现在你的 Qwen2.5-VL 已经完全配置好了 fps=1 和 384x384 分辨率！🎉
