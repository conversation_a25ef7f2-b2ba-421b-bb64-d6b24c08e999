#!/usr/bin/env python3
"""
Example script to run Qwen2.5-VL experiment with fps=1 and 224x224 resolution
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_experiment_with_lmms_eval():
    """
    Run experiment using lmms-eval framework
    """
    print("Running Qwen2.5-VL experiment with fps=1 and 384x384 resolution")
    print("="*60)
    
    # Example command for running with lmms-eval
    cmd_template = """
python -m lmms_eval \\
    --model qwen2_5_vl \\
    --model_args pretrained=Qwen/Qwen2.5-VL-7B-Instruct,max_pixels={max_pixels},fps={fps},max_image_size={max_image_size},use_custom_video_loader=True \\
    --tasks {tasks} \\
    --batch_size 1 \\
    --log_samples \\
    --log_samples_suffix qwen2_5_vl_fps1_224x224 \\
    --output_path ./results/
    """
    
    # Configuration
    max_pixels = 384 * 384  # 384x384 resolution
    fps = 1.0  # 1 fps
    max_image_size = 384  # For custom video loader
    
    # Example tasks (you can modify these based on your needs)
    example_tasks = [
        "mvr",  # Multi-modal Video Reasoning
        "video_mme",  # Video-MME
        "videochatgpt",  # VideoChatGPT
        "nextqa",  # NExT-QA
    ]
    
    print(f"Configuration:")
    print(f"  - Max pixels: {max_pixels} (384x384)")
    print(f"  - FPS: {fps}")
    print(f"  - Max image size: {max_image_size}")
    print()
    
    for task in example_tasks:
        cmd = cmd_template.format(
            max_pixels=max_pixels,
            fps=fps,
            max_image_size=max_image_size,
            tasks=task
        )
        
        print(f"Command for {task}:")
        print(cmd.strip())
        print("-" * 40)

def run_direct_model_test():
    """
    Run direct model test
    """
    print("\nDirect Model Test")
    print("="*30)
    
    try:
        from qwen2_5_vl import Qwen2_5_VL
        
        # Initialize model with your specific configuration
        model = Qwen2_5_VL(
            pretrained="Qwen/Qwen2.5-VL-7B-Instruct",
            device="cuda",
            batch_size=1,
            max_pixels=384 * 384,  # 384x384 resolution
            fps=1.0,  # 1 fps
            max_image_size=384,  # For custom video loader
            use_custom_video_loader=True,  # Use custom loader for better control
        )
        
        print("✓ Model loaded successfully with configuration:")
        print(f"  - Max pixels: {model.max_pixels}")
        print(f"  - FPS: {model.fps}")
        print(f"  - Max image size: {model.max_image_size}")
        print(f"  - Custom video loader: {model.use_custom_video_loader}")
        
        # Example of how to process a video (pseudo-code)
        print("\nExample usage:")
        print("""
# To process a video with your model:
# 1. Prepare your video file
video_path = "path/to/your/video.mp4"
question = "What is happening in this video?"

# 2. Create a request (this would be done by lmms-eval framework)
# The model will automatically use fps=1 and 224x224 resolution
# based on the configuration we set
        """)
        
    except Exception as e:
        print(f"✗ Error loading model: {e}")
        print("Make sure you have the required dependencies installed:")
        print("  - transformers")
        print("  - torch")
        print("  - qwen-vl-utils")
        print("  - decord")

def main():
    """Main function"""
    print("Qwen2.5-VL Experiment Setup")
    print("Configuration: fps=1, resolution=384x384")
    print("="*50)
    
    # Show lmms-eval commands
    run_experiment_with_lmms_eval()
    
    # Test direct model loading
    run_direct_model_test()
    
    print("\n" + "="*50)
    print("IMPORTANT NOTES:")
    print("="*50)
    print("1. The model is now configured to use:")
    print("   - FPS: 1 (extracts 1 frame per second)")
    print("   - Resolution: 384x384 pixels")
    print("   - This provides better quality while still being efficient")
    print()
    print("2. To run your experiment, use one of the lmms-eval commands above")
    print("   or modify them based on your specific task requirements.")
    print()
    print("3. Make sure your video dataset is accessible and properly formatted.")
    print()
    print("4. Monitor GPU memory usage - 384x384 resolution provides a good")
    print("   balance between quality and memory efficiency.")

if __name__ == "__main__":
    main()
